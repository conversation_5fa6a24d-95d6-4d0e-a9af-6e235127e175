# File Cleanup Timing Fix for MagicGateway Streaming Export

## Problem Analysis

The MagicGateway export functionality was experiencing a critical file cleanup timing issue that caused streaming exports to fail with "file not found" errors.

### Root Cause

The issue occurred because:

1. **Premature Cleanup**: The Parquet file cleanup was happening immediately after the export function was called, but before the streaming process had finished reading the file.

2. **Synchronous vs Asynchronous Mismatch**: The cleanup logic assumed that once the export function returned, the file was no longer needed. However, with streaming exports, the file needs to remain available throughout the entire streaming process.

3. **Multiple Cleanup Points**: There were several places in the code where cleanup was happening too early:
   - In `generate_intelligent_export_response` (async_export.py)
   - In `export_job_data` function (scripts.py)
   - In data iterator `finally` blocks

### Error Sequence

```
1. Parquet file created: E:\Temp\ch_native_export_ff2553a2-9005-49ea-b3b7-399be71e12d4_data_vab6duuz.parquet (266M rows, 824MB)
2. Export function called: generate_period_separated_excel_response_streaming()
3. Function returns immediately (streaming starts in background)
4. Cleanup called at line 614: "Cleaned up Parquet file" 
5. File deleted from disk
6. Streaming process tries to read file: FileNotFoundError
7. Export fails
```

## Solution Implementation

### 1. Modified Streaming Functions

**Updated Functions:**
- `generate_streaming_excel_response()` - Added `cleanup_source_file_path` parameter
- `generate_period_separated_excel_response_streaming()` - Added `cleanup_source_file` parameter

**Key Changes:**
```python
# OLD: Cleanup in data iterator
async def data_iterator():
    try:
        async for chunk in read_parquet_file_async_streaming(file_path):
            yield chunk
    finally:
        cleanup_source()  # ❌ Too early!

# NEW: Cleanup in background task
async def cleanup_export():
    await asyncio.gather(data_task, stream_task, return_exceptions=True)
    if cleanup_source_file_path:
        cleanup_parquet_file(cleanup_source_file_path)  # ✅ After streaming complete
```

### 2. Background Task Cleanup

The cleanup now happens in the `BackgroundTask` that's attached to the `StreamingResponse`:

```python
return EnhancedStreamingResponse(
    content_generator=stream_generator(),
    filename=filename,
    background=BackgroundTask(cleanup_export),  # ✅ Cleanup after response complete
)
```

### 3. Updated Export Pipeline

**In `async_export.py`:**
- Removed premature `cleanup_source()` calls from data iterators
- Added cleanup parameters to streaming functions
- Moved cleanup logic to background tasks

**In `scripts.py`:**
- Updated to use new streaming functions
- Removed immediate cleanup calls
- Added cleanup parameters to function calls

### 4. Cleanup Timing Flow

**New Cleanup Sequence:**
```
1. Parquet file created
2. Streaming export function called with cleanup_source_file=True
3. Function returns StreamingResponse immediately
4. Background tasks start:
   - Data processing task (reads Parquet file)
   - File streaming task (streams Excel to client)
5. Client receives streaming data
6. After both tasks complete, cleanup task runs
7. Parquet file deleted ✅
```

## Code Changes Summary

### Files Modified

1. **`src/magic_gateway/utils/async_export.py`**
   - Added cleanup parameters to streaming functions
   - Moved cleanup logic to background tasks
   - Removed premature cleanup from data iterators

2. **`src/magic_gateway/api/v1/endpoints/scripts.py`**
   - Updated to use new streaming functions
   - Removed immediate cleanup calls
   - Added proper imports for streaming functions

3. **`tests/unit/test_file_cleanup_timing.py`** (New)
   - Comprehensive tests for cleanup timing
   - Validates file availability during streaming
   - Tests cleanup behavior

### Key Function Updates

**`generate_streaming_excel_response()`:**
```python
# Added parameter
cleanup_source_file_path: Optional[str] = None

# Added cleanup in background task
if cleanup_source_file_path:
    cleanup_parquet_file(cleanup_source_file_path)
```

**`generate_period_separated_excel_response_streaming()`:**
```python
# Added parameter  
cleanup_source_file: bool = False

# Added cleanup in background task
if cleanup_source_file:
    cleanup_parquet_file(parquet_file_path)
```

## Testing and Validation

### Unit Tests

Created comprehensive tests in `test_file_cleanup_timing.py`:

1. **File Availability Test**: Verifies file remains available during streaming
2. **Period-Separated Test**: Tests period-separated export timing
3. **Cleanup Timing Test**: Validates cleanup happens after streaming
4. **Cleanup Function Test**: Tests cleanup function works correctly
5. **No Cleanup Test**: Tests streaming without cleanup enabled

### Manual Testing

To test the fix manually:

1. **Create large export** (500K+ rows)
2. **Monitor file system** during export
3. **Verify file exists** throughout streaming process
4. **Confirm cleanup** happens after completion

### Expected Behavior

**Before Fix:**
- ❌ File deleted immediately after function call
- ❌ Streaming fails with "file not found"
- ❌ Export process fails

**After Fix:**
- ✅ File remains available during streaming
- ✅ Streaming completes successfully  
- ✅ File cleaned up after completion
- ✅ No memory leaks or disk space issues

## Performance Impact

### Memory Usage
- **No change** in memory efficiency
- **Maintains** streaming benefits
- **Preserves** constant memory usage

### Disk Usage
- **Temporary increase** during export (file remains until complete)
- **Automatic cleanup** prevents long-term disk usage
- **No permanent impact** on disk space

### Processing Speed
- **No performance degradation**
- **Maintains** streaming speed benefits
- **Background cleanup** doesn't block response

## Backward Compatibility

### Legacy Functions
- **Preserved** all existing function signatures
- **Added optional parameters** with default values
- **No breaking changes** for existing code

### Migration Path
- **Automatic** for new exports (uses streaming by default)
- **Gradual migration** for existing code
- **Deprecation warnings** for legacy functions

## Error Handling

### Cleanup Failures
```python
try:
    cleanup_parquet_file(cleanup_source_file_path)
    log.info(f"Cleaned up source file: {cleanup_source_file_path}")
except Exception as cleanup_error:
    log.error(f"Error cleaning up source file: {cleanup_error}", exc_info=True)
```

### File Access Errors
- **Graceful handling** of file access issues
- **Detailed logging** for troubleshooting
- **No impact** on export success

## Monitoring and Logging

### Enhanced Logging
```
Export {export_id}: Starting streaming data processing
Export {export_id}: Completed data processing - 500,000 rows in 45.2s
Export {export_id}: Starting file streaming  
Export {export_id}: File streaming completed
Export {export_id}: Cleaned up source Parquet file: /path/to/file.parquet
```

### Key Metrics
- **File lifecycle tracking**
- **Cleanup timing logs**
- **Error rate monitoring**
- **Performance metrics**

## Conclusion

The file cleanup timing fix resolves the critical issue where streaming exports failed due to premature file deletion. The solution:

- ✅ **Fixes the root cause** by moving cleanup to background tasks
- ✅ **Maintains performance** and memory efficiency
- ✅ **Preserves compatibility** with existing code
- ✅ **Adds comprehensive testing** for reliability
- ✅ **Provides proper error handling** and logging

This fix ensures that MagicGateway's streaming export functionality works reliably for large datasets while maintaining proper resource cleanup.
