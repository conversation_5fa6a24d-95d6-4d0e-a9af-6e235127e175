#!/usr/bin/env python3
"""
Memory efficiency benchmark for MagicGateway export functionality.

This script demonstrates the memory efficiency improvements achieved by the streaming
export pipeline compared to the legacy approach.
"""

import os
import sys
import tempfile
import time
import asyncio
import psutil
import pyarrow as pa
import pyarrow.parquet as pq
from typing import List, Dict, Any

# Add the src directory to the path so we can import MagicGateway modules
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from magic_gateway.utils.parquet_processor import (
    read_parquet_file_in_chunks,
    read_parquet_file_async,
    read_parquet_file_async_streaming,
    get_memory_usage_mb,
)


def create_test_parquet_file(num_rows: int, num_columns: int = 10) -> str:
    """Create a test Parquet file with specified dimensions."""
    print(f"Creating test Parquet file with {num_rows:,} rows and {num_columns} columns...")
    
    # Generate test data
    data = {}
    for i in range(num_columns):
        if i == 0:
            data[f'id'] = list(range(num_rows))
        elif i == 1:
            data[f'period'] = [f'2024-{(j % 12) + 1:02d}' for j in range(num_rows)]
        elif i % 3 == 0:
            data[f'text_col_{i}'] = [f'text_value_{j}_{i}' for j in range(num_rows)]
        elif i % 3 == 1:
            data[f'numeric_col_{i}'] = [j * 1.5 + i for j in range(num_rows)]
        else:
            data[f'category_col_{i}'] = [f'cat_{j % 10}_{i}' for j in range(num_rows)]
    
    # Create PyArrow table
    table = pa.table(data)
    
    # Create temporary file
    temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.parquet')
    temp_file.close()
    
    # Write to Parquet file
    pq.write_table(table, temp_file.name)
    
    file_size_mb = os.path.getsize(temp_file.name) / (1024 * 1024)
    print(f"Created Parquet file: {temp_file.name} ({file_size_mb:.1f} MB)")
    
    return temp_file.name


def benchmark_legacy_approach(file_path: str) -> Dict[str, Any]:
    """Benchmark the legacy (non-streaming) approach."""
    print("\n=== Benchmarking Legacy Approach ===")
    
    initial_memory = get_memory_usage_mb()
    start_time = time.time()
    
    try:
        # Use legacy function that loads entire file into memory
        chunks = read_parquet_file_in_chunks(file_path, chunk_size=100000)
        
        total_rows = sum(len(chunk) for chunk in chunks)
        peak_memory = get_memory_usage_mb()
        
        end_time = time.time()
        
        result = {
            'approach': 'Legacy (Load All)',
            'total_rows': total_rows,
            'initial_memory_mb': initial_memory,
            'peak_memory_mb': peak_memory,
            'memory_increase_mb': peak_memory - initial_memory,
            'duration_seconds': end_time - start_time,
            'chunks_in_memory': len(chunks),
        }
        
        print(f"Total rows processed: {total_rows:,}")
        print(f"Initial memory: {initial_memory:.1f} MB")
        print(f"Peak memory: {peak_memory:.1f} MB")
        print(f"Memory increase: {peak_memory - initial_memory:.1f} MB")
        print(f"Duration: {end_time - start_time:.2f} seconds")
        print(f"Chunks in memory: {len(chunks)}")
        
        return result
        
    except Exception as e:
        print(f"Error in legacy approach: {e}")
        return {
            'approach': 'Legacy (Load All)',
            'error': str(e),
            'initial_memory_mb': initial_memory,
            'peak_memory_mb': get_memory_usage_mb(),
        }


def benchmark_streaming_approach(file_path: str) -> Dict[str, Any]:
    """Benchmark the new streaming approach."""
    print("\n=== Benchmarking Streaming Approach ===")
    
    initial_memory = get_memory_usage_mb()
    start_time = time.time()
    max_memory = initial_memory
    
    try:
        total_rows = 0
        chunk_count = 0
        
        # Use streaming function that processes data in chunks without loading all into memory
        for chunk in read_parquet_file_async_streaming(file_path, chunk_size=100000):
            total_rows += len(chunk)
            chunk_count += 1
            
            current_memory = get_memory_usage_mb()
            max_memory = max(max_memory, current_memory)
            
            # Simulate some processing
            if chunk_count % 5 == 0:
                print(f"  Processed {total_rows:,} rows, current memory: {current_memory:.1f} MB")
        
        end_time = time.time()
        
        result = {
            'approach': 'Streaming',
            'total_rows': total_rows,
            'initial_memory_mb': initial_memory,
            'peak_memory_mb': max_memory,
            'memory_increase_mb': max_memory - initial_memory,
            'duration_seconds': end_time - start_time,
            'chunks_processed': chunk_count,
        }
        
        print(f"Total rows processed: {total_rows:,}")
        print(f"Initial memory: {initial_memory:.1f} MB")
        print(f"Peak memory: {max_memory:.1f} MB")
        print(f"Memory increase: {max_memory - initial_memory:.1f} MB")
        print(f"Duration: {end_time - start_time:.2f} seconds")
        print(f"Chunks processed: {chunk_count}")
        
        return result
        
    except Exception as e:
        print(f"Error in streaming approach: {e}")
        return {
            'approach': 'Streaming',
            'error': str(e),
            'initial_memory_mb': initial_memory,
            'peak_memory_mb': get_memory_usage_mb(),
        }


async def benchmark_async_streaming_approach(file_path: str) -> Dict[str, Any]:
    """Benchmark the new async streaming approach."""
    print("\n=== Benchmarking Async Streaming Approach ===")
    
    initial_memory = get_memory_usage_mb()
    start_time = time.time()
    max_memory = initial_memory
    
    try:
        total_rows = 0
        chunk_count = 0
        
        # Use async streaming function
        async for chunk in read_parquet_file_async_streaming(file_path, chunk_size=100000):
            total_rows += len(chunk)
            chunk_count += 1
            
            current_memory = get_memory_usage_mb()
            max_memory = max(max_memory, current_memory)
            
            # Simulate some processing
            if chunk_count % 5 == 0:
                print(f"  Processed {total_rows:,} rows, current memory: {current_memory:.1f} MB")
        
        end_time = time.time()
        
        result = {
            'approach': 'Async Streaming',
            'total_rows': total_rows,
            'initial_memory_mb': initial_memory,
            'peak_memory_mb': max_memory,
            'memory_increase_mb': max_memory - initial_memory,
            'duration_seconds': end_time - start_time,
            'chunks_processed': chunk_count,
        }
        
        print(f"Total rows processed: {total_rows:,}")
        print(f"Initial memory: {initial_memory:.1f} MB")
        print(f"Peak memory: {max_memory:.1f} MB")
        print(f"Memory increase: {max_memory - initial_memory:.1f} MB")
        print(f"Duration: {end_time - start_time:.2f} seconds")
        print(f"Chunks processed: {chunk_count}")
        
        return result
        
    except Exception as e:
        print(f"Error in async streaming approach: {e}")
        return {
            'approach': 'Async Streaming',
            'error': str(e),
            'initial_memory_mb': initial_memory,
            'peak_memory_mb': get_memory_usage_mb(),
        }


def print_comparison(results: List[Dict[str, Any]]):
    """Print comparison of benchmark results."""
    print("\n" + "="*80)
    print("BENCHMARK COMPARISON RESULTS")
    print("="*80)
    
    for result in results:
        if 'error' in result:
            print(f"\n{result['approach']}: ERROR - {result['error']}")
            continue
            
        print(f"\n{result['approach']}:")
        print(f"  Total rows: {result['total_rows']:,}")
        print(f"  Memory increase: {result['memory_increase_mb']:.1f} MB")
        print(f"  Duration: {result['duration_seconds']:.2f} seconds")
        print(f"  Rate: {result['total_rows'] / result['duration_seconds']:.0f} rows/sec")
    
    # Calculate improvements
    if len(results) >= 2 and 'error' not in results[0] and 'error' not in results[1]:
        legacy = results[0]
        streaming = results[1]
        
        memory_improvement = legacy['memory_increase_mb'] - streaming['memory_increase_mb']
        memory_improvement_pct = (memory_improvement / legacy['memory_increase_mb']) * 100 if legacy['memory_increase_mb'] > 0 else 0
        
        print(f"\nIMPROVEMENTS:")
        print(f"  Memory reduction: {memory_improvement:.1f} MB ({memory_improvement_pct:.1f}%)")
        
        if streaming['duration_seconds'] < legacy['duration_seconds']:
            time_improvement = legacy['duration_seconds'] - streaming['duration_seconds']
            time_improvement_pct = (time_improvement / legacy['duration_seconds']) * 100
            print(f"  Time reduction: {time_improvement:.2f} seconds ({time_improvement_pct:.1f}%)")


async def main():
    """Main benchmark function."""
    print("MagicGateway Memory Efficiency Benchmark")
    print("="*50)
    
    # Configuration
    num_rows = 500000  # 500K rows
    num_columns = 15   # 15 columns
    
    print(f"Test configuration:")
    print(f"  Rows: {num_rows:,}")
    print(f"  Columns: {num_columns}")
    print(f"  Initial system memory: {get_memory_usage_mb():.1f} MB")
    
    # Create test file
    test_file = create_test_parquet_file(num_rows, num_columns)
    
    try:
        results = []
        
        # Benchmark legacy approach
        legacy_result = benchmark_legacy_approach(test_file)
        results.append(legacy_result)
        
        # Wait a bit and force garbage collection
        import gc
        gc.collect()
        time.sleep(2)
        
        # Benchmark streaming approach
        streaming_result = benchmark_streaming_approach(test_file)
        results.append(streaming_result)
        
        # Wait a bit and force garbage collection
        gc.collect()
        time.sleep(2)
        
        # Benchmark async streaming approach
        async_result = await benchmark_async_streaming_approach(test_file)
        results.append(async_result)
        
        # Print comparison
        print_comparison(results)
        
    finally:
        # Cleanup
        if os.path.exists(test_file):
            os.unlink(test_file)
            print(f"\nCleaned up test file: {test_file}")


if __name__ == "__main__":
    asyncio.run(main())
