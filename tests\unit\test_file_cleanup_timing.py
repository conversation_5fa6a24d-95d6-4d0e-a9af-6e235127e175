"""
Tests for file cleanup timing in streaming export functionality.

This test verifies that the file cleanup timing issue has been fixed and that
Parquet files remain available during the entire streaming export process.
"""

import os
import tempfile
import asyncio
import pytest
import pyarrow as pa
import pyarrow.parquet as pq
from unittest.mock import patch, MagicMock, AsyncMock

from magic_gateway.utils.async_export import (
    generate_streaming_excel_response,
    generate_period_separated_excel_response_streaming,
)
from magic_gateway.utils.parquet_processor import (
    read_parquet_file_async_streaming,
    cleanup_parquet_file,
)


@pytest.fixture
def test_parquet_file():
    """Create a test Parquet file."""
    # Create sample data
    data = {
        'id': list(range(1000)),
        'name': [f'item_{i}' for i in range(1000)],
        'value': [i * 1.5 for i in range(1000)],
        'period': [f'2024-{(i % 12) + 1:02d}' for i in range(1000)],
    }
    
    # Create PyArrow table
    table = pa.table(data)
    
    # Create temporary file
    temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.parquet')
    temp_file.close()
    
    # Write to Parquet file
    pq.write_table(table, temp_file.name)
    
    yield temp_file.name
    
    # Cleanup
    if os.path.exists(temp_file.name):
        os.unlink(temp_file.name)


class TestFileCleanupTiming:
    """Test file cleanup timing in streaming exports."""
    
    @pytest.mark.asyncio
    async def test_file_remains_available_during_streaming(self, test_parquet_file):
        """Test that the Parquet file remains available during streaming."""
        file_access_log = []
        
        # Mock the streaming Excel writer to track file access
        with patch('magic_gateway.utils.async_export.StreamingExcelWriter') as mock_writer_class:
            mock_writer = MagicMock()
            mock_writer.save.return_value = tempfile.mktemp(suffix='.xlsx')
            mock_writer_class.return_value = mock_writer
            
            # Track when the file is accessed during streaming
            original_streaming_func = read_parquet_file_async_streaming
            
            async def tracking_streaming_func(file_path, chunk_size=None):
                """Wrapper that tracks file access."""
                file_access_log.append(f"Starting to read: {file_path}")
                
                # Verify file exists at start
                assert os.path.exists(file_path), f"File should exist at start: {file_path}"
                
                chunk_count = 0
                async for chunk in original_streaming_func(file_path, chunk_size):
                    chunk_count += 1
                    file_access_log.append(f"Reading chunk {chunk_count}, file exists: {os.path.exists(file_path)}")
                    
                    # File should still exist during streaming
                    assert os.path.exists(file_path), f"File should exist during streaming: {file_path}"
                    
                    yield chunk
                
                file_access_log.append(f"Finished reading: {file_path}")
                # File should still exist after streaming data iterator completes
                assert os.path.exists(file_path), f"File should exist after streaming: {file_path}"
            
            # Patch the streaming function
            with patch('magic_gateway.utils.async_export.read_parquet_file_async_streaming', tracking_streaming_func):
                # Create data iterator
                async def data_iterator():
                    async for chunk in tracking_streaming_func(test_parquet_file):
                        yield chunk
                
                # Generate streaming Excel response with cleanup enabled
                response = await generate_streaming_excel_response(
                    data_iterator=data_iterator(),
                    filename="test_export.xlsx",
                    cleanup_source_file_path=test_parquet_file,
                )
                
                # Verify response was created
                assert response is not None
                
                # File should still exist immediately after function returns
                # (cleanup happens in background task)
                assert os.path.exists(test_parquet_file), "File should exist immediately after function returns"
                
                # Verify file access log shows proper sequence
                assert len(file_access_log) > 0, "File access should have been logged"
                assert "Starting to read" in file_access_log[0]
                assert "Finished reading" in file_access_log[-1]
    
    @pytest.mark.asyncio
    async def test_period_separated_streaming_file_timing(self, test_parquet_file):
        """Test file timing in period-separated streaming export."""
        
        # Mock the streaming Excel writer
        with patch('magic_gateway.utils.async_export.StreamingExcelWriter') as mock_writer_class:
            mock_writer = MagicMock()
            mock_writer.save.return_value = tempfile.mktemp(suffix='.xlsx')
            mock_writer_class.return_value = mock_writer
            
            # Mock the period column identification
            with patch('magic_gateway.utils.async_export.identify_period_column', return_value='period'):
                
                # Generate period-separated streaming Excel response with cleanup
                response = await generate_period_separated_excel_response_streaming(
                    parquet_file_path=test_parquet_file,
                    filename="test_period_export.xlsx",
                    cleanup_source_file=True,
                )
                
                # Verify response was created
                assert response is not None
                
                # File should still exist immediately after function returns
                # (cleanup happens in background task)
                assert os.path.exists(test_parquet_file), "File should exist immediately after function returns"
    
    @pytest.mark.asyncio
    async def test_cleanup_happens_after_streaming_completes(self, test_parquet_file):
        """Test that cleanup only happens after streaming is complete."""
        cleanup_called = []
        
        # Mock cleanup function to track when it's called
        def mock_cleanup(file_path):
            cleanup_called.append(file_path)
            # Actually delete the file to simulate real cleanup
            if os.path.exists(file_path):
                os.unlink(file_path)
        
        with patch('magic_gateway.utils.async_export.cleanup_parquet_file', mock_cleanup):
            with patch('magic_gateway.utils.async_export.StreamingExcelWriter') as mock_writer_class:
                mock_writer = MagicMock()
                mock_writer.save.return_value = tempfile.mktemp(suffix='.xlsx')
                mock_writer_class.return_value = mock_writer
                
                # Create data iterator
                async def data_iterator():
                    async for chunk in read_parquet_file_async_streaming(test_parquet_file):
                        yield chunk
                
                # Generate streaming Excel response with cleanup enabled
                response = await generate_streaming_excel_response(
                    data_iterator=data_iterator(),
                    filename="test_export.xlsx",
                    cleanup_source_file_path=test_parquet_file,
                )
                
                # Cleanup should not have been called yet
                assert len(cleanup_called) == 0, "Cleanup should not be called immediately"
                
                # File should still exist
                assert os.path.exists(test_parquet_file), "File should still exist"
    
    def test_cleanup_function_works(self, test_parquet_file):
        """Test that the cleanup function properly removes files."""
        # Verify file exists
        assert os.path.exists(test_parquet_file)
        
        # Call cleanup
        cleanup_parquet_file(test_parquet_file)
        
        # Verify file is removed
        assert not os.path.exists(test_parquet_file)
    
    @pytest.mark.asyncio
    async def test_streaming_without_cleanup(self, test_parquet_file):
        """Test streaming export without cleanup enabled."""
        
        with patch('magic_gateway.utils.async_export.StreamingExcelWriter') as mock_writer_class:
            mock_writer = MagicMock()
            mock_writer.save.return_value = tempfile.mktemp(suffix='.xlsx')
            mock_writer_class.return_value = mock_writer
            
            # Create data iterator
            async def data_iterator():
                async for chunk in read_parquet_file_async_streaming(test_parquet_file):
                    yield chunk
            
            # Generate streaming Excel response WITHOUT cleanup
            response = await generate_streaming_excel_response(
                data_iterator=data_iterator(),
                filename="test_export.xlsx",
                cleanup_source_file_path=None,  # No cleanup
            )
            
            # Verify response was created
            assert response is not None
            
            # File should still exist (no cleanup requested)
            assert os.path.exists(test_parquet_file), "File should still exist when cleanup is disabled"


if __name__ == "__main__":
    pytest.main([__file__])
