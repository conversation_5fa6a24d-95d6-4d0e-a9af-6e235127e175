"""
Tests for streaming export functionality and memory efficiency improvements.
"""

import os
import tempfile
import asyncio
import pytest
import pyarrow as pa
import pyarrow.parquet as pq
import psutil
from unittest.mock import patch, MagicMock

from magic_gateway.utils.parquet_processor import (
    read_parquet_file_streaming,
    read_parquet_file_async_streaming,
    calculate_adaptive_chunk_size,
    monitor_memory_and_adjust_chunk_size,
    StreamingProgressTracker,
    get_memory_usage_mb,
)
from magic_gateway.utils.async_export import (
    generate_streaming_excel_response,
    generate_period_separated_excel_response_streaming,
)


@pytest.fixture
def sample_parquet_file():
    """Create a sample Parquet file for testing."""
    # Create sample data
    data = {
        'id': list(range(1000)),
        'name': [f'item_{i}' for i in range(1000)],
        'value': [i * 1.5 for i in range(1000)],
        'period': [f'2024-{(i % 12) + 1:02d}' for i in range(1000)],
        'category': [f'cat_{i % 5}' for i in range(1000)]
    }
    
    # Create PyArrow table
    table = pa.table(data)
    
    # Create temporary file
    temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.parquet')
    temp_file.close()
    
    # Write to Parquet file
    pq.write_table(table, temp_file.name)
    
    yield temp_file.name
    
    # Cleanup
    if os.path.exists(temp_file.name):
        os.unlink(temp_file.name)


@pytest.fixture
def large_parquet_file():
    """Create a larger Parquet file for memory testing."""
    # Create larger sample data (50K rows)
    data = {
        'id': list(range(50000)),
        'name': [f'item_{i}' for i in range(50000)],
        'value': [i * 1.5 for i in range(50000)],
        'period': [f'2024-{(i % 12) + 1:02d}' for i in range(50000)],
        'category': [f'cat_{i % 10}' for i in range(50000)],
        'description': [f'description_for_item_{i}_with_some_longer_text' for i in range(50000)]
    }
    
    # Create PyArrow table
    table = pa.table(data)
    
    # Create temporary file
    temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.parquet')
    temp_file.close()
    
    # Write to Parquet file
    pq.write_table(table, temp_file.name)
    
    yield temp_file.name
    
    # Cleanup
    if os.path.exists(temp_file.name):
        os.unlink(temp_file.name)


class TestAdaptiveChunkSizing:
    """Test adaptive chunk sizing functionality."""
    
    def test_calculate_adaptive_chunk_size_basic(self):
        """Test basic adaptive chunk size calculation."""
        file_size = 1024 * 1024  # 1MB
        available_memory = 1000  # 1000MB
        
        chunk_size = calculate_adaptive_chunk_size(file_size, available_memory)
        
        assert chunk_size >= 50000  # MIN_CHUNK_SIZE
        assert chunk_size <= 1000000  # MAX_CHUNK_SIZE
    
    def test_calculate_adaptive_chunk_size_with_columns(self):
        """Test adaptive chunk size calculation with column information."""
        file_size = 1024 * 1024  # 1MB
        available_memory = 1000  # 1000MB
        num_columns = 10
        
        chunk_size = calculate_adaptive_chunk_size(file_size, available_memory, num_columns)
        
        assert chunk_size >= 50000  # MIN_CHUNK_SIZE
        assert chunk_size <= 1000000  # MAX_CHUNK_SIZE
    
    def test_monitor_memory_and_adjust_chunk_size_high_usage(self):
        """Test chunk size reduction when memory usage is high."""
        current_chunk_size = 100000
        memory_usage = 2800  # 93% of 3000MB threshold
        
        new_chunk_size = monitor_memory_and_adjust_chunk_size(current_chunk_size, memory_usage)
        
        assert new_chunk_size < current_chunk_size
        assert new_chunk_size >= 50000  # MIN_CHUNK_SIZE
    
    def test_monitor_memory_and_adjust_chunk_size_low_usage(self):
        """Test chunk size increase when memory usage is low."""
        current_chunk_size = 100000
        memory_usage = 600  # 20% of 3000MB threshold
        
        new_chunk_size = monitor_memory_and_adjust_chunk_size(current_chunk_size, memory_usage)
        
        assert new_chunk_size > current_chunk_size
        assert new_chunk_size <= 1000000  # MAX_CHUNK_SIZE


class TestStreamingProgressTracker:
    """Test streaming progress tracker functionality."""
    
    def test_progress_tracker_basic(self):
        """Test basic progress tracking functionality."""
        tracker = StreamingProgressTracker("Test Operation", total_rows=1000)
        
        # Update progress
        tracker.update(100)
        assert tracker.processed_rows == 100
        
        tracker.update(200)
        assert tracker.processed_rows == 300
        
        # Complete operation
        tracker.complete()
    
    def test_progress_tracker_without_total(self):
        """Test progress tracking without known total."""
        tracker = StreamingProgressTracker("Test Operation")
        
        # Update progress
        tracker.update(100)
        assert tracker.processed_rows == 100
        
        tracker.complete()


class TestStreamingParquetReader:
    """Test streaming Parquet reading functionality."""
    
    def test_read_parquet_file_streaming_basic(self, sample_parquet_file):
        """Test basic streaming Parquet file reading."""
        total_rows = 0
        chunk_count = 0
        
        # Read file in streaming fashion
        for chunk in read_parquet_file_streaming(sample_parquet_file, chunk_size=100):
            assert isinstance(chunk, list)
            assert len(chunk) > 0
            assert isinstance(chunk[0], dict)
            
            total_rows += len(chunk)
            chunk_count += 1
        
        assert total_rows == 1000  # Expected total rows
        assert chunk_count > 1  # Should be multiple chunks
    
    @pytest.mark.asyncio
    async def test_read_parquet_file_async_streaming(self, sample_parquet_file):
        """Test async streaming Parquet file reading."""
        total_rows = 0
        chunk_count = 0
        
        # Read file in async streaming fashion
        async for chunk in read_parquet_file_async_streaming(sample_parquet_file, chunk_size=100):
            assert isinstance(chunk, list)
            assert len(chunk) > 0
            assert isinstance(chunk[0], dict)
            
            total_rows += len(chunk)
            chunk_count += 1
        
        assert total_rows == 1000  # Expected total rows
        assert chunk_count > 1  # Should be multiple chunks


class TestMemoryEfficiency:
    """Test memory efficiency of streaming operations."""
    
    def test_memory_usage_during_streaming(self, large_parquet_file):
        """Test that memory usage remains reasonable during streaming."""
        initial_memory = get_memory_usage_mb()
        max_memory_usage = initial_memory
        
        total_rows = 0
        
        # Read large file in streaming fashion
        for chunk in read_parquet_file_streaming(large_parquet_file, chunk_size=1000):
            current_memory = get_memory_usage_mb()
            max_memory_usage = max(max_memory_usage, current_memory)
            total_rows += len(chunk)
        
        memory_increase = max_memory_usage - initial_memory
        
        # Memory increase should be reasonable (less than 500MB for 50K rows)
        assert memory_increase < 500, f"Memory usage increased by {memory_increase:.1f}MB"
        assert total_rows == 50000  # Expected total rows
    
    @pytest.mark.asyncio
    async def test_async_memory_usage_during_streaming(self, large_parquet_file):
        """Test that memory usage remains reasonable during async streaming."""
        initial_memory = get_memory_usage_mb()
        max_memory_usage = initial_memory
        
        total_rows = 0
        
        # Read large file in async streaming fashion
        async for chunk in read_parquet_file_async_streaming(large_parquet_file, chunk_size=1000):
            current_memory = get_memory_usage_mb()
            max_memory_usage = max(max_memory_usage, current_memory)
            total_rows += len(chunk)
        
        memory_increase = max_memory_usage - initial_memory
        
        # Memory increase should be reasonable (less than 500MB for 50K rows)
        assert memory_increase < 500, f"Memory usage increased by {memory_increase:.1f}MB"
        assert total_rows == 50000  # Expected total rows


class TestStreamingExcelExport:
    """Test streaming Excel export functionality."""
    
    @pytest.mark.asyncio
    async def test_streaming_excel_response_creation(self, sample_parquet_file):
        """Test that streaming Excel response can be created."""
        # Create async data iterator
        async def data_iterator():
            async for chunk in read_parquet_file_async_streaming(sample_parquet_file, chunk_size=100):
                yield chunk
        
        # Mock job info
        job_info = {
            'job_id': 'test_job_123',
            'query': 'SELECT * FROM test_table',
            'user': 'test_user'
        }
        
        # Generate streaming Excel response
        response = await generate_streaming_excel_response(
            data_iterator=data_iterator(),
            filename="test_export.xlsx",
            job_info=job_info,
            horizontal_facts=False
        )
        
        assert response is not None
        assert hasattr(response, 'content_generator')


if __name__ == "__main__":
    pytest.main([__file__])
