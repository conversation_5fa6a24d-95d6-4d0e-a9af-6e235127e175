"""
Tests for Excel temporary file management in streaming export functionality.

This test verifies that the Excel temporary file management issue has been fixed
and that Excel files remain available during the entire streaming process.
"""

import os
import tempfile
import asyncio
import pytest
import pyarrow as pa
import pyarrow.parquet as pq
from unittest.mock import patch, MagicMock, AsyncMock

from magic_gateway.utils.async_export import (
    generate_streaming_excel_response,
    generate_period_separated_excel_response_streaming,
)
from magic_gateway.utils.parquet_processor import (
    read_parquet_file_async_streaming,
)


@pytest.fixture
def test_parquet_file():
    """Create a test Parquet file."""
    # Create sample data
    data = {
        'id': list(range(1000)),
        'name': [f'item_{i}' for i in range(1000)],
        'value': [i * 1.5 for i in range(1000)],
        'period': [f'2024-{(i % 12) + 1:02d}' for i in range(1000)],
    }
    
    # Create PyArrow table
    table = pa.table(data)
    
    # Create temporary file
    temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.parquet')
    temp_file.close()
    
    # Write to Parquet file
    pq.write_table(table, temp_file.name)
    
    yield temp_file.name
    
    # Cleanup
    if os.path.exists(temp_file.name):
        os.unlink(temp_file.name)


class TestExcelTempFileManagement:
    """Test Excel temporary file management in streaming exports."""
    
    @pytest.mark.asyncio
    async def test_excel_file_remains_available_during_streaming(self, test_parquet_file):
        """Test that Excel temp files remain available during streaming."""
        excel_file_access_log = []
        excel_temp_file_path = None
        
        # Mock StreamingExcelWriter to track file lifecycle
        class MockStreamingExcelWriter:
            def __init__(self, filename, horizontal_facts=False):
                self.filename = filename
                self.horizontal_facts = horizontal_facts
                self.temp_file_path = tempfile.mktemp(suffix='.xlsx')
                # Create a dummy Excel file
                with open(self.temp_file_path, 'wb') as f:
                    f.write(b'PK\x03\x04')  # ZIP signature for Excel
                excel_file_access_log.append(f"Excel file created: {self.temp_file_path}")
                nonlocal excel_temp_file_path
                excel_temp_file_path = self.temp_file_path
                
            def add_info_sheet(self, info):
                excel_file_access_log.append(f"Info sheet added, file exists: {os.path.exists(self.temp_file_path)}")
                
            def write_data_chunk(self, chunk):
                excel_file_access_log.append(f"Data chunk written, file exists: {os.path.exists(self.temp_file_path)}")
                
            def save(self):
                excel_file_access_log.append(f"Excel file saved, file exists: {os.path.exists(self.temp_file_path)}")
                return self.temp_file_path
                
            def cleanup(self):
                excel_file_access_log.append(f"Cleanup called, file exists before: {os.path.exists(self.temp_file_path)}")
                if os.path.exists(self.temp_file_path):
                    os.unlink(self.temp_file_path)
                excel_file_access_log.append(f"Cleanup completed, file exists after: {os.path.exists(self.temp_file_path)}")
        
        # Patch the StreamingExcelWriter
        with patch('magic_gateway.utils.async_export.StreamingExcelWriter', MockStreamingExcelWriter):
            
            # Create data iterator
            async def data_iterator():
                async for chunk in read_parquet_file_async_streaming(test_parquet_file, chunk_size=100):
                    yield chunk
            
            # Generate streaming Excel response
            response = await generate_streaming_excel_response(
                data_iterator=data_iterator(),
                filename="test_export.xlsx",
                cleanup_source_file_path=None,  # No source cleanup for this test
            )
            
            # Verify response was created
            assert response is not None
            
            # Excel file should still exist immediately after function returns
            # (cleanup happens in background task)
            assert os.path.exists(excel_temp_file_path), "Excel file should exist immediately after function returns"
            
            # Verify file access log shows proper sequence
            assert len(excel_file_access_log) > 0, "Excel file access should have been logged"
            assert "Excel file created" in excel_file_access_log[0]
            assert "Excel file saved" in str(excel_file_access_log)
            
            # Cleanup should not have been called yet
            cleanup_logs = [log for log in excel_file_access_log if "Cleanup called" in log]
            assert len(cleanup_logs) == 0, "Cleanup should not be called immediately"
    
    @pytest.mark.asyncio
    async def test_period_separated_excel_file_timing(self, test_parquet_file):
        """Test Excel file timing in period-separated streaming export."""
        excel_file_access_log = []
        excel_temp_file_path = None
        
        # Mock StreamingExcelWriter
        class MockStreamingExcelWriter:
            def __init__(self, filename, horizontal_facts=False):
                self.filename = filename
                self.temp_file_path = tempfile.mktemp(suffix='.xlsx')
                with open(self.temp_file_path, 'wb') as f:
                    f.write(b'PK\x03\x04')
                excel_file_access_log.append(f"Excel file created: {self.temp_file_path}")
                nonlocal excel_temp_file_path
                excel_temp_file_path = self.temp_file_path
                
            def add_info_sheet(self, info):
                pass
                
            def write_data_chunk(self, chunk):
                pass
                
            def save(self):
                excel_file_access_log.append(f"Excel file saved, file exists: {os.path.exists(self.temp_file_path)}")
                return self.temp_file_path
                
            def cleanup(self):
                excel_file_access_log.append(f"Cleanup called")
                if os.path.exists(self.temp_file_path):
                    os.unlink(self.temp_file_path)
        
        # Mock the period column identification
        with patch('magic_gateway.utils.async_export.identify_period_column', return_value='period'):
            with patch('magic_gateway.utils.async_export.StreamingExcelWriter', MockStreamingExcelWriter):
                
                # Generate period-separated streaming Excel response
                response = await generate_period_separated_excel_response_streaming(
                    parquet_file_path=test_parquet_file,
                    filename="test_period_export.xlsx",
                    cleanup_source_file=False,  # No source cleanup for this test
                )
                
                # Verify response was created
                assert response is not None
                
                # Excel file should still exist immediately after function returns
                assert os.path.exists(excel_temp_file_path), "Excel file should exist immediately after function returns"
                
                # Cleanup should not have been called yet
                cleanup_logs = [log for log in excel_file_access_log if "Cleanup called" in log]
                assert len(cleanup_logs) == 0, "Cleanup should not be called immediately"
    
    @pytest.mark.asyncio
    async def test_excel_cleanup_happens_after_streaming(self, test_parquet_file):
        """Test that Excel cleanup only happens after streaming is complete."""
        cleanup_called = []
        excel_temp_file_path = None
        
        # Mock StreamingExcelWriter with cleanup tracking
        class MockStreamingExcelWriter:
            def __init__(self, filename, horizontal_facts=False):
                self.temp_file_path = tempfile.mktemp(suffix='.xlsx')
                with open(self.temp_file_path, 'wb') as f:
                    f.write(b'PK\x03\x04')
                nonlocal excel_temp_file_path
                excel_temp_file_path = self.temp_file_path
                
            def add_info_sheet(self, info):
                pass
                
            def write_data_chunk(self, chunk):
                pass
                
            def save(self):
                return self.temp_file_path
                
            def cleanup(self):
                cleanup_called.append(self.temp_file_path)
                if os.path.exists(self.temp_file_path):
                    os.unlink(self.temp_file_path)
        
        with patch('magic_gateway.utils.async_export.StreamingExcelWriter', MockStreamingExcelWriter):
            
            # Create data iterator
            async def data_iterator():
                async for chunk in read_parquet_file_async_streaming(test_parquet_file, chunk_size=100):
                    yield chunk
            
            # Generate streaming Excel response
            response = await generate_streaming_excel_response(
                data_iterator=data_iterator(),
                filename="test_export.xlsx",
                cleanup_source_file_path=None,
            )
            
            # Cleanup should not have been called yet
            assert len(cleanup_called) == 0, "Excel cleanup should not be called immediately"
            
            # File should still exist
            assert os.path.exists(excel_temp_file_path), "Excel file should still exist"
    
    def test_excel_file_handle_management(self):
        """Test that Excel file handles are properly managed."""
        from magic_gateway.utils.streaming_excel_writer import StreamingExcelWriter
        
        # Create a real StreamingExcelWriter instance
        writer = StreamingExcelWriter("test_file.xlsx")
        
        # Verify temp file was created
        assert os.path.exists(writer.temp_file_path), "Temp file should be created"
        
        # Write some data
        test_data = [{'id': 1, 'name': 'test', 'value': 1.5}]
        writer.write_data_chunk(test_data)
        
        # Save the file
        saved_path = writer.save()
        assert saved_path == writer.temp_file_path
        assert os.path.exists(saved_path), "Saved file should exist"
        
        # File should be readable after save
        with open(saved_path, 'rb') as f:
            content = f.read(4)
            assert content == b'PK\x03\x04', "File should have Excel ZIP signature"
        
        # Cleanup should remove the file
        writer.cleanup()
        assert not os.path.exists(saved_path), "File should be removed after cleanup"


if __name__ == "__main__":
    pytest.main([__file__])
